// diagnostico-supabase.js
const fs = require('fs');
const path = require('path');

console.log('=== DIAGNÓSTICO DE CONFIGURACIÓN DE SUPABASE ===');
console.log('Fecha y hora:', new Date().toLocaleString());
console.log('\n');

// Leer el archivo .env
try {
  const envPath = path.join(__dirname, '.env');
  const envContent = fs.readFileSync(envPath, 'utf8');
  console.log('✅ Archivo .env encontrado');
  
  // Verificar variables de Supabase
  const supabaseUrlMatch = envContent.match(/VITE_SUPABASE_URL=(.+)/);
  const supabaseKeyMatch = envContent.match(/VITE_SUPABASE_ANON_KEY=(.+)/);
  
  if (supabaseUrlMatch && supabaseUrlMatch[1]) {
    const url = supabaseUrlMatch[1].trim();
    console.log('✅ VITE_SUPABASE_URL encontrada:', url);
    
    // Verificar formato de URL
    try {
      new URL(url);
      console.log('✅ El formato de la URL es válido');
      
      // Verificar que sea una URL de Supabase
      if (url.includes('supabase.co')) {
        console.log('✅ La URL corresponde a un proyecto de Supabase');
      } else {
        console.warn('⚠️ La URL no parece ser de un proyecto de Supabase');
      }
    } catch (e) {
      console.error('❌ El formato de la URL no es válido');
    }
  } else {
    console.error('❌ VITE_SUPABASE_URL no encontrada en el archivo .env');
  }
  
  if (supabaseKeyMatch && supabaseKeyMatch[1]) {
    const key = supabaseKeyMatch[1].trim();
    console.log('✅ VITE_SUPABASE_ANON_KEY encontrada');
    
    // Verificar formato de la clave (generalmente comienza con eyJ)
    if (key.startsWith('eyJ')) {
      console.log('✅ El formato de la clave anónima parece correcto');
    } else {
      console.warn('⚠️ El formato de la clave anónima no parece ser el estándar de Supabase');
    }
  } else {
    console.error('❌ VITE_SUPABASE_ANON_KEY no encontrada en el archivo .env');
  }
  
  // Buscar variables de React (para compatibilidad)
  const reactUrlMatch = envContent.match(/REACT_APP_SUPABASE_URL=(.+)/);
  const reactKeyMatch = envContent.match(/REACT_APP_SUPABASE_ANON_KEY=(.+)/);
  
  if (!reactUrlMatch) {
    console.warn('⚠️ REACT_APP_SUPABASE_URL no encontrada. Esto podría causar problemas en proyectos que usan Create React App.');
  }
  
  if (!reactKeyMatch) {
    console.warn('⚠️ REACT_APP_SUPABASE_ANON_KEY no encontrada. Esto podría causar problemas en proyectos que usan Create React App.');
  }
  
} catch (error) {
  console.error('❌ Error al leer el archivo .env:', error.message);
}

// Verificar archivos de configuración de Supabase
console.log('\n=== VERIFICANDO ARCHIVOS DE CONFIGURACIÓN ===');

// Verificar supabaseConfig.js en src/api
try {
  const configPath = path.join(__dirname, 'src', 'api', 'supabaseConfig.js');
  if (fs.existsSync(configPath)) {
    console.log('✅ Archivo src/api/supabaseConfig.js encontrado');
  } else {
    console.warn('⚠️ Archivo src/api/supabaseConfig.js no encontrado');
  }
} catch (error) {
  console.error('❌ Error al verificar supabaseConfig.js:', error.message);
}

// Verificar supabase.js en src/api
try {
  const clientPath = path.join(__dirname, 'src', 'api', 'supabase.js');
  if (fs.existsSync(clientPath)) {
    console.log('✅ Archivo src/api/supabase.js encontrado');
  } else {
    console.warn('⚠️ Archivo src/api/supabase.js no encontrado');
  }
} catch (error) {
  console.error('❌ Error al verificar supabase.js:', error.message);
}

// Verificar supabaseClient.js en src/api
try {
  const clientPath = path.join(__dirname, 'src', 'api', 'supabaseClient.js');
  if (fs.existsSync(clientPath)) {
    console.log('✅ Archivo src/api/supabaseClient.js encontrado');
  } else {
    console.warn('⚠️ Archivo src/api/supabaseClient.js no encontrado');
  }
} catch (error) {
  console.error('❌ Error al verificar supabaseClient.js:', error.message);
}

// Verificar archivos en prueba1/supabase-react-app
console.log('\n=== VERIFICANDO ARCHIVOS DE PRUEBA1/SUPABASE-REACT-APP ===');

// Verificar supabaseConfig.js
try {
  const configPath = path.join(__dirname, 'prueba1', 'supabase-react-app', 'src', 'supabaseConfig.js');
  if (fs.existsSync(configPath)) {
    console.log('✅ Archivo prueba1/supabase-react-app/src/supabaseConfig.js encontrado');
    
    // Leer el archivo para verificar si usa las variables correctas
    const configContent = fs.readFileSync(configPath, 'utf8');
    if (configContent.includes('process.env.REACT_APP_SUPABASE_URL')) {
      console.warn('⚠️ Este archivo usa REACT_APP_SUPABASE_URL en lugar de VITE_SUPABASE_URL');
    }
  } else {
    console.warn('⚠️ Archivo prueba1/supabase-react-app/src/supabaseConfig.js no encontrado');
  }
} catch (error) {
  console.error('❌ Error al verificar prueba1/supabase-react-app/src/supabaseConfig.js:', error.message);
}

console.log('\n=== RECOMENDACIONES ===');
console.log('1. Asegúrate de que las variables VITE_SUPABASE_URL y VITE_SUPABASE_ANON_KEY estén correctamente definidas en el archivo .env');
console.log('2. Si tienes proyectos que usan Create React App, considera agregar también REACT_APP_SUPABASE_URL y REACT_APP_SUPABASE_ANON_KEY');
console.log('3. Verifica que los archivos de configuración de Supabase estén importando las variables de entorno correctamente');
console.log('4. Para proyectos con Vite, usa import.meta.env.VITE_SUPABASE_URL');
console.log('5. Para proyectos con Create React App, usa process.env.REACT_APP_SUPABASE_URL');