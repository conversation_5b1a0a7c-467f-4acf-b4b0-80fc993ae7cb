import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * Componente que proporciona transiciones suaves entre páginas
 */
const PageTransition = ({ children }) => {
  const location = useLocation();
  const [displayLocation, setDisplayLocation] = useState(location);
  const [transitionStage, setTransitionStage] = useState('fadeIn');
  
  useEffect(() => {
    // Si la ubicación ha cambiado
    if (location.pathname !== displayLocation.pathname) {
      // Iniciar la transición de salida
      setTransitionStage('fadeOut');
      
      // Después de la transición de salida, actualizar la ubicación y comenzar la transición de entrada
      const timeout = setTimeout(() => {
        setDisplayLocation(location);
        setTransitionStage('fadeIn');
      }, 300); // Debe coincidir con la duración de la transición CSS
      
      return () => clearTimeout(timeout);
    }
  }, [location, displayLocation]);
  
  return (
    <div className={`page-transition ${transitionStage}`}>
      {children}
    </div>
  );
};

export default PageTransition;
