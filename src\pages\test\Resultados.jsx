// src/pages/test/Resultados.jsx
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTest } from '../../context/TestContext';
// import { aplicaciones } from '../../api/supabase'; // Comentado para resolver error
import Loading from '../../components/common/Loading';

// Quitamos temporalmente la importación de recharts
// import {
//   BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend,
//   ResponsiveContainer, RadarChart, PolarGrid, PolarAngleAxis,
//   PolarRadiusAxis, Radar
// } from 'recharts';

const ResultadosPage = () => {
  const navigate = useNavigate();
  // Eliminamos la referencia a Redux
  // const user = useSelector(state => state.auth.user);
  const user = null; // Inicialmente establecemos el usuario como null
  const {
    resultados,
    aplicacionId,
    testCompletado,
    cargarResultados,
    cargando
  } = useTest();

  const [loading, setLoading] = useState(true);
  const [pacienteInfo, setPacienteInfo] = useState(null);
  const [dataChart, setDataChart] = useState([]);
  const [radarData, setRadarData] = useState([]);
  const [informe, setInforme] = useState({
    fortalezas: [],
    areas_mejora: [],
    recomendaciones: []
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Si no hay resultados, intentar cargarlos
        if (!resultados && aplicacionId) {
          await cargarResultados(aplicacionId);
        }

        // Cargar información del paciente y la aplicación
        if (aplicacionId) {
          // Comentado para evitar error con aplicaciones
          // const { data, error } = await aplicaciones.getById(aplicacionId);
          
          // Datos de ejemplo para desarrollo
          const data = {
            paciente: {
              nombre: "Usuario",
              apellido: "Ejemplo",
              edad: 25,
              sexo: "Masculino",
              nivel_educativo: "Universitario"
            },
            fecha_inicio: new Date()
          };
          const error = null;

          if (error) {
            throw error;
          }

          if (data) {
            setPacienteInfo({
              nombre: `${data.paciente.nombre} ${data.paciente.apellido}`,
              edad: data.paciente.edad,
              sexo: data.paciente.sexo,
              nivel: data.paciente.nivel_educativo,
              fecha: new Date(data.fecha_inicio).toLocaleDateString(),
            });
          }
        }

        // Si tenemos resultados, prepararlos para visualización
        if (resultados) {
          // Preparar datos para el gráfico de barras
          const chartData = [];
          const radar = [];

          Object.entries(resultados).forEach(([id, resultado]) => {
            chartData.push({
              name: resultado.codigo,
              PD: resultado.puntuacionDirecta,
              PC: resultado.puntuacionCentil,
              Interpretacion: resultado.interpretacion
            });

            radar.push({
              subject: resultado.codigo,
              A: resultado.puntuacionCentil,
              fullMark: 100
            });
          });

          setDataChart(chartData);
          setRadarData(radar);

          // Generar informe cualitativo
          generarInformeCualitativo(resultados);
        }
      } catch (error) {
        console.error('Error al cargar resultados:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [aplicacionId, resultados, cargarResultados]);

  // Función para generar informe cualitativo
  const generarInformeCualitativo = (resultados) => {
    const fortalezas = [];
    const areas_mejora = [];
    const recomendaciones = [];

    // Procesar resultados para identificar fortalezas y áreas de mejora
    Object.entries(resultados).forEach(([id, resultado]) => {
      const { codigo, nombre, puntuacionCentil, interpretacion } = resultado;

      // Interpretar según los percentiles
      if (puntuacionCentil >= 70) {
        fortalezas.push({
          codigo,
          nombre,
          interpretacion: `${interpretacion} (PC: ${puntuacionCentil})`,
          descripcion: getDescripcionAptitud(codigo, true)
        });
      } else if (puntuacionCentil <= 30) {
        areas_mejora.push({
          codigo,
          nombre,
          interpretacion: `${interpretacion} (PC: ${puntuacionCentil})`,
          descripcion: getDescripcionAptitud(codigo, false)
        });
      }
    });

    // Generar recomendaciones basadas en áreas de mejora
    areas_mejora.forEach(area => {
      recomendaciones.push({
        codigo: area.codigo,
        recomendacion: getRecomendacion(area.codigo)
      });
    });

    setInforme({ fortalezas, areas_mejora, recomendaciones });
  };

  // Función para obtener descripción de aptitud
  const getDescripcionAptitud = (codigo, fortaleza) => {
    const descripciones = {
      V: {
        fortaleza: "Alta capacidad para comprender, utilizar y analizar el lenguaje escrito y hablado.",
        debilidad: "Dificultades para comprender conceptos expresados a través de palabras."
      },
      E: {
        fortaleza: "Excelente capacidad para visualizar y manipular mentalmente formas y patrones espaciales.",
        debilidad: "Dificultades para comprender relaciones espaciales y visualizar objetos en diferentes dimensiones."
      },
      A: {
        fortaleza: "Gran capacidad para mantener el foco en tareas específicas, detectando detalles con precisión.",
        debilidad: "Dificultad para mantener la concentración y detectar detalles específicos en tareas que requieren atención sostenida."
      },
      R: {
        fortaleza: "Destacada habilidad para identificar patrones lógicos y resolver problemas mediante el razonamiento.",
        debilidad: "Dificultades para identificar reglas lógicas y establecer inferencias en situaciones nuevas."
      },
      N: {
        fortaleza: "Excelente capacidad para comprender y manipular conceptos numéricos y resolver problemas matemáticos.",
        debilidad: "Dificultades en el manejo de conceptos numéricos y operaciones matemáticas básicas."
      },
      M: {
        fortaleza: "Buena comprensión de principios físicos y mecánicos básicos aplicados a situaciones cotidianas.",
        debilidad: "Dificultades para comprender el funcionamiento de dispositivos mecánicos y principios físicos básicos."
      },
      O: {
        fortaleza: "Excelente dominio de las reglas ortográficas y alta precisión en la escritura.",
        debilidad: "Dificultades con las reglas ortográficas y tendencia a cometer errores en la escritura."
      }
    };

    return descripciones[codigo] ?
      (fortaleza ? descripciones[codigo].fortaleza : descripciones[codigo].debilidad) :
      "No hay descripción disponible.";
  };

  // Función para obtener recomendaciones según el código
  const getRecomendacion = (codigo) => {
    const recomendaciones = {
      V: "Fomentar la lectura diaria y realizar actividades que enriquezcan el vocabulario como juegos de palabras, debates y redacción.",
      E: "Practicar con rompecabezas, ejercicios de rotación mental, dibujo técnico y actividades que involucren navegación espacial.",
      A: "Realizar ejercicios de mindfulness, practicar tareas que requieran concentración por períodos cortos e ir aumentando gradualmente el tiempo.",
      R: "Resolver acertijos lógicos, participar en juegos de estrategia y analizar problemas complejos dividiéndolos en partes más sencillas.",
      N: "Practicar operaciones matemáticas diariamente, resolver problemas aplicados a la vida real y utilizar juegos que involucren cálculos.",
      M: "Construir modelos, experimentar con el funcionamiento de objetos cotidianos y estudiar los principios básicos de la física.",
      O: "Realizar ejercicios de dictado, revisión de textos y practicar la escritura consciente prestando atención a las reglas ortográficas."
    };

    return recomendaciones[codigo] || "No hay recomendaciones específicas disponibles.";
  };

  // Si está cargando, mostrar pantalla de carga
  if (loading || cargando) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loading fullScreen={true} message="Cargando resultados..." />
      </div>
    );
  }

  // Si no hay resultados, mostrar mensaje
  if (!resultados || Object.keys(resultados).length === 0) {
    return (
      <div className="flex items-center justify-center h-screen bg-blue-50">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md">
          <div className="text-center">
            <svg className="mx-auto h-16 w-16 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h2 className="mt-4 text-xl font-bold text-gray-800">No hay resultados disponibles</h2>
            <p className="mt-2 text-gray-600">
              No se han encontrado resultados para mostrar. Es posible que aún no hayas completado el test o que haya ocurrido un error.
            </p>
            <div className="mt-6">
              <button
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                onClick={() => navigate('/test')}
              >
                Volver a Tests
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Renderizar página de resultados
  return (
    <div className="min-h-screen bg-blue-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        {/* Cabecera */}
        <div className="bg-white rounded-lg shadow-md mb-6 p-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
            <div>
              <h1 className="text-2xl font-bold text-blue-800">Resultados del Test BAT-7</h1>
              <p className="text-gray-600 mt-1">
                Evaluación de Aptitudes - Nivel Elemental
              </p>
            </div>

            {pacienteInfo && (
              <div className="mt-4 md:mt-0 bg-blue-50 p-3 rounded-md">
                <h3 className="font-semibold text-blue-800">Información del Estudiante</h3>
                <div className="text-sm text-gray-700 mt-1">
                  <p><span className="font-medium">Nombre:</span> {pacienteInfo.nombre}</p>
                  <p><span className="font-medium">Edad:</span> {pacienteInfo.edad} años</p>
                  <p><span className="font-medium">Nivel:</span> {pacienteInfo.nivel}</p>
                  <p><span className="font-medium">Fecha:</span> {pacienteInfo.fecha}</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Gráficos de Resultados */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {/* Gráfico de Barras */}
          <div className="bg-white rounded-lg shadow-md p-4">
            <h2 className="text-lg font-semibold text-gray-800 mb-4">Puntuaciones Centiles</h2>
            <div className="h-80 flex items-center justify-center bg-gray-100">
              {/* Componente eliminado temporalmente: BarChart */}
              <p className="text-gray-500">Gráfico no disponible. Instale la dependencia recharts.</p>
            </div>
            <div className="text-xs text-gray-500 mt-2 text-center">
              PC: Puntuación Centil (percentil) - Indica la posición relativa respecto a la población de referencia
            </div>
          </div>

          {/* Gráfico de Radar */}
          <div className="bg-white rounded-lg shadow-md p-4">
            <h2 className="text-lg font-semibold text-gray-800 mb-4">Perfil de Aptitudes</h2>
            <div className="h-80 flex items-center justify-center bg-gray-100">
              {/* Componente eliminado temporalmente: RadarChart */}
              <p className="text-gray-500">Gráfico no disponible. Instale la dependencia recharts.</p>
            </div>
            <div className="text-xs text-gray-500 mt-2 text-center">
              El perfil muestra las fortalezas y áreas de mejora en las diferentes aptitudes evaluadas
            </div>
          </div>
        </div>

        {/* Tabla de Resultados */}
        <div className="bg-white rounded-lg shadow-md mb-6 overflow-hidden">
          <h2 className="text-lg font-semibold p-4 bg-gray-50 border-b">Puntuaciones por Aptitud</h2>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Aptitud
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Código
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    PD
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    PC
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Interpretación
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {Object.entries(resultados).map(([id, resultado]) => (
                  <tr key={id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{resultado.nombre}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">{resultado.codigo}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{resultado.puntuacionDirecta}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{resultado.puntuacionCentil}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                        ${resultado.puntuacionCentil >= 70 ? 'bg-green-100 text-green-800' :
                        resultado.puntuacionCentil <= 30 ? 'bg-red-100 text-red-800' :
                        'bg-yellow-100 text-yellow-800'}`}
                      >
                        {resultado.interpretacion}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <div className="p-4 text-xs text-gray-500 bg-gray-50 border-t">
            <p><span className="font-medium">PD:</span> Puntuación Directa - Número de respuestas correctas</p>
            <p><span className="font-medium">PC:</span> Puntuación Centil - Posición relativa respecto a la población de referencia (1-100)</p>
          </div>
        </div>

        {/* Informe Cualitativo */}
        <div className="bg-white rounded-lg shadow-md mb-6">
          <h2 className="text-lg font-semibold p-4 bg-gray-50 border-b">Informe Cualitativo</h2>

          <div className="p-4">
            {/* Fortalezas */}
            <div className="mb-6">
              <h3 className="text-md font-medium text-green-700 mb-2">Fortalezas</h3>
              {informe.fortalezas.length > 0 ? (
                <div className="space-y-3">
                  {informe.fortalezas.map((fortaleza, index) => (
                    <div key={index} className="bg-green-50 p-3 rounded-md">
                      <div className="font-medium text-green-800">
                        {fortaleza.codigo} - {fortaleza.nombre}: {fortaleza.interpretacion}
                      </div>
                      <p className="text-sm text-green-700 mt-1">{fortaleza.descripcion}</p>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-600 italic">No se identificaron fortalezas destacadas.</p>
              )}
            </div>

            {/* Áreas de Mejora */}
            <div className="mb-6">
              <h3 className="text-md font-medium text-red-700 mb-2">Áreas de Mejora</h3>
              {informe.areas_mejora.length > 0 ? (
                <div className="space-y-3">
                  {informe.areas_mejora.map((area, index) => (
                    <div key={index} className="bg-red-50 p-3 rounded-md">
                      <div className="font-medium text-red-800">
                        {area.codigo} - {area.nombre}: {area.interpretacion}
                      </div>
                      <p className="text-sm text-red-700 mt-1">{area.descripcion}</p>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-600 italic">No se identificaron áreas de mejora significativas.</p>
              )}
            </div>

            {/* Recomendaciones */}
            <div>
              <h3 className="text-md font-medium text-blue-700 mb-2">Recomendaciones</h3>
              {informe.recomendaciones.length > 0 ? (
                <div className="space-y-3">
                  {informe.recomendaciones.map((rec, index) => (
                    <div key={index} className="bg-blue-50 p-3 rounded-md">
                      <div className="font-medium text-blue-800">{rec.codigo}</div>
                      <p className="text-sm text-blue-700 mt-1">{rec.recomendacion}</p>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-600 italic">No hay recomendaciones específicas en este momento.</p>
              )}
            </div>
          </div>
        </div>

        {/* Acciones */}
        <div className="flex flex-col sm:flex-row justify-between space-y-3 sm:space-y-0 sm:space-x-3">
          <button
            className="px-4 py-2 bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            onClick={() => navigate('/test')}
          >
            Volver a Tests
          </button>

          <div className="flex space-x-3">
            <button
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              onClick={() => window.print()}
            >
              Imprimir Resultados
            </button>

            <button
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              onClick={() => alert('Función de exportar a PDF en desarrollo')}
            >
              Exportar a PDF
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResultadosPage;