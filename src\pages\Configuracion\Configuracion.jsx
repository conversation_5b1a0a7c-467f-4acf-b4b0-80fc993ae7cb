import React, { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';
import { supabase } from '../../api/supabaseClient';
import { toast } from 'react-toastify';
import UserManagement from '../../components/settings/UserManagement';
import { FaUser, FaLock, FaCog, FaUsers } from 'react-icons/fa';

const Configuracion = () => {
  const { user, userRole } = useAuth();
  const [userData, setUserData] = useState({
    nombre: '',
    apellido: '',
    email: '',
    documento: '',
    rol: ''
  });
  const [password, setPassword] = useState({
    current: '',
    new: '',
    confirm: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [activeTab, setActiveTab] = useState('profile');

  useEffect(() => {
    if (user) {
      fetchUserData();
    }
  }, [user]);

  const fetchUserData = async () => {
    try {
      setLoading(true);
      
      // Obtener datos del usuario desde la tabla usuarios
      const { data, error } = await supabase
        .from('usuarios')
        .select('*')
        .eq('id', user.id)
        .single();
      
      if (error) throw error;
      
      setUserData({
        nombre: data.nombre || '',
        apellido: data.apellido || '',
        email: user.email || '',
        documento: data.documento || '',
        rol: data.rol || ''
      });
    } catch (error) {
      console.error('Error al obtener datos del usuario:', error);
      setError('No se pudieron cargar los datos del usuario');
    } finally {
      setLoading(false);
    }
  };

  const handleProfileChange = (e) => {
    const { name, value } = e.target;
    setUserData(prev => ({ ...prev, [name]: value }));
  };

  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setPassword(prev => ({ ...prev, [name]: value }));
  };

  const handleProfileSubmit = async (e) => {
    e.preventDefault();
    
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      
      // Actualizar datos del usuario en la tabla usuarios
      const { error } = await supabase
        .from('usuarios')
        .update({
          nombre: userData.nombre,
          apellido: userData.apellido,
          documento: userData.documento
        })
        .eq('id', user.id);
      
      if (error) throw error;
      
      setSuccess('Perfil actualizado correctamente');
      toast.success('Perfil actualizado correctamente');
    } catch (error) {
      console.error('Error al actualizar perfil:', error);
      setError('Error al actualizar perfil: ' + error.message);
      toast.error('Error al actualizar perfil: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordSubmit = async (e) => {
    e.preventDefault();
    
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      
      // Validar que las contraseñas coincidan
      if (password.new !== password.confirm) {
        setError('Las contraseñas no coinciden');
        return;
      }
      
      // Cambiar contraseña
      const { error } = await supabase.auth.updateUser({
        password: password.new
      });
      
      if (error) throw error;
      
      // Limpiar formulario
      setPassword({
        current: '',
        new: '',
        confirm: ''
      });
      
      setSuccess('Contraseña actualizada correctamente');
      toast.success('Contraseña actualizada correctamente');
    } catch (error) {
      console.error('Error al cambiar contraseña:', error);
      setError('Error al cambiar contraseña: ' + error.message);
      toast.error('Error al cambiar contraseña: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Determinar si el usuario es administrador
  const isAdmin = userRole === 'administrador' || userData.rol === 'administrador';

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold text-gray-800 mb-6">Configuración</h1>
      
      <div className="bg-white shadow rounded-lg">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex" aria-label="Tabs">
            <button
              onClick={() => setActiveTab('profile')}
              className={`${
                activeTab === 'profile'
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } flex items-center py-4 px-6 text-center border-b-2 font-medium text-sm`}
            >
              <FaUser className="mr-2" /> Perfil
            </button>
            <button
              onClick={() => setActiveTab('security')}
              className={`${
                activeTab === 'security'
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } flex items-center py-4 px-6 text-center border-b-2 font-medium text-sm`}
            >
              <FaLock className="mr-2" /> Seguridad
            </button>
            {isAdmin && (
              <button
                onClick={() => setActiveTab('users')}
                className={`${
                  activeTab === 'users'
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } flex items-center py-4 px-6 text-center border-b-2 font-medium text-sm`}
              >
                <FaUsers className="mr-2" /> Usuarios
              </button>
            )}
            {isAdmin && (
              <button
                onClick={() => setActiveTab('system')}
                className={`${
                  activeTab === 'system'
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } flex items-center py-4 px-6 text-center border-b-2 font-medium text-sm`}
              >
                <FaCog className="mr-2" /> Sistema
              </button>
            )}
          </nav>
        </div>
        
        <div className="p-6">
          {activeTab === 'profile' && (
            <div>
              <h2 className="text-lg font-semibold text-gray-800 mb-4">Información de Perfil</h2>
              
              {error && activeTab === 'profile' && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                  <span className="block sm:inline">{error}</span>
                  <button 
                    className="absolute top-0 bottom-0 right-0 px-4 py-3"
                    onClick={() => setError(null)}
                  >
                    <span className="text-red-500">×</span>
                  </button>
                </div>
              )}
              
              {success && activeTab === 'profile' && (
                <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                  <span className="block sm:inline">{success}</span>
                  <button 
                    className="absolute top-0 bottom-0 right-0 px-4 py-3"
                    onClick={() => setSuccess(null)}
                  >
                    <span className="text-green-500">×</span>
                  </button>
                </div>
              )}
              
              <form onSubmit={handleProfileSubmit}>
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <div>
                    <label htmlFor="nombre" className="block text-sm font-medium text-gray-700">
                      Nombre
                    </label>
                    <input
                      type="text"
                      name="nombre"
                      id="nombre"
                      value={userData.nombre}
                      onChange={handleProfileChange}
                      className="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="apellido" className="block text-sm font-medium text-gray-700">
                      Apellido
                    </label>
                    <input
                      type="text"
                      name="apellido"
                      id="apellido"
                      value={userData.apellido}
                      onChange={handleProfileChange}
                      className="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                      Correo Electrónico
                    </label>
                    <input
                      type="email"
                      name="email"
                      id="email"
                      value={userData.email}
                      disabled
                      className="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md bg-gray-100"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      El correo electrónico no se puede cambiar
                    </p>
                  </div>
                  
                  <div>
                    <label htmlFor="documento" className="block text-sm font-medium text-gray-700">
                      Documento de Identidad
                    </label>
                    <input
                      type="text"
                      name="documento"
                      id="documento"
                      value={userData.documento}
                      onChange={handleProfileChange}
                      className="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="rol" className="block text-sm font-medium text-gray-700">
                      Rol
                    </label>
                    <input
                      type="text"
                      name="rol"
                      id="rol"
                      value={userData.rol}
                      disabled
                      className="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md bg-gray-100"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      El rol solo puede ser cambiado por un administrador
                    </p>
                  </div>
                </div>
                
                <div className="pt-6">
                  <button
                    type="submit"
                    className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    disabled={loading}
                  >
                    {loading ? 'Guardando...' : 'Guardar Cambios'}
                  </button>
                </div>
              </form>
            </div>
          )}
          
          {activeTab === 'security' && (
            <div>
              <h2 className="text-lg font-semibold text-gray-800 mb-4">Cambiar Contraseña</h2>
              
              {error && activeTab === 'security' && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                  <span className="block sm:inline">{error}</span>
                  <button 
                    className="absolute top-0 bottom-0 right-0 px-4 py-3"
                    onClick={() => setError(null)}
                  >
                    <span className="text-red-500">×</span>
                  </button>
                </div>
              )}
              
              {success && activeTab === 'security' && (
                <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                  <span className="block sm:inline">{success}</span>
                  <button 
                    className="absolute top-0 bottom-0 right-0 px-4 py-3"
                    onClick={() => setSuccess(null)}
                  >
                    <span className="text-green-500">×</span>
                  </button>
                </div>
              )}
              
              <form onSubmit={handlePasswordSubmit}>
                <div className="space-y-4">
                  <div>
                    <label htmlFor="current" className="block text-sm font-medium text-gray-700">
                      Contraseña Actual
                    </label>
                    <input
                      type="password"
                      name="current"
                      id="current"
                      value={password.current}
                      onChange={handlePasswordChange}
                      className="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                      required
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="new" className="block text-sm font-medium text-gray-700">
                      Nueva Contraseña
                    </label>
                    <input
                      type="password"
                      name="new"
                      id="new"
                      value={password.new}
                      onChange={handlePasswordChange}
                      className="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                      required
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="confirm" className="block text-sm font-medium text-gray-700">
                      Confirmar Nueva Contraseña
                    </label>
                    <input
                      type="password"
                      name="confirm"
                      id="confirm"
                      value={password.confirm}
                      onChange={handlePasswordChange}
                      className="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                      required
                    />
                  </div>
                </div>
                
                <div className="pt-6">
                  <button
                    type="submit"
                    className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    disabled={loading}
                  >
                    {loading ? 'Cambiando...' : 'Cambiar Contraseña'}
                  </button>
                </div>
              </form>
            </div>
          )}
          
          {activeTab === 'users' && isAdmin && (
            <UserManagement />
          )}
          
          {activeTab === 'system' && isAdmin && (
            <div>
              <h2 className="text-lg font-semibold text-gray-800 mb-4">Configuración del Sistema</h2>
              
              <div className="space-y-6">
                <div>
                  <h3 className="text-md font-medium text-gray-800 mb-2">Correo Electrónico</h3>
                  <div className="grid grid-cols-1 gap-4">
                    <div className="flex items-center">
                      <input
                        id="email_notifications"
                        type="checkbox"
                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                      />
                      <label htmlFor="email_notifications" className="ml-2 block text-sm text-gray-700">
                        Enviar notificaciones por correo
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="welcome_email"
                        type="checkbox"
                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        defaultChecked
                      />
                      <label htmlFor="welcome_email" className="ml-2 block text-sm text-gray-700">
                        Enviar correo de bienvenida a nuevos usuarios
                      </label>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-md font-medium text-gray-800 mb-2">Seguridad</h3>
                  <div className="grid grid-cols-1 gap-4">
                    <div className="flex items-center">
                      <input
                        id="two_factor"
                        type="checkbox"
                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                      />
                      <label htmlFor="two_factor" className="ml-2 block text-sm text-gray-700">
                        Habilitar autenticación de dos factores
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="password_expiry"
                        type="checkbox"
                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                      />
                      <label htmlFor="password_expiry" className="ml-2 block text-sm text-gray-700">
                        Expiración de contraseñas cada 90 días
                      </label>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-md font-medium text-gray-800 mb-2">Cuestionarios</h3>
                  <div className="grid grid-cols-1 gap-4">
                    <div className="flex items-center">
                      <input
                        id="auto_publish"
                        type="checkbox"
                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                      />
                      <label htmlFor="auto_publish" className="ml-2 block text-sm text-gray-700">
                        Publicar automáticamente nuevos cuestionarios
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="allow_editing"
                        type="checkbox"
                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        defaultChecked
                      />
                      <label htmlFor="allow_editing" className="ml-2 block text-sm text-gray-700">
                        Permitir a psicólogos editar cuestionarios publicados
                      </label>
                    </div>
                  </div>
                </div>
                
                <div className="pt-4">
                  <button
                    type="button"
                    className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    Guardar Configuración
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Configuracion;
