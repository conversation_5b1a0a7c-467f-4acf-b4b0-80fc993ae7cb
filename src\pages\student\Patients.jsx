import React, { useState, useEffect } from 'react';
import supabase from '../../api/supabaseClient';
import { Card, CardHeader, CardBody } from '../../components/ui/Card';
import PatientCard from '../../components/patient/PatientCard';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON>ch, <PERSON>aUser } from 'react-icons/fa';

const Patients = () => {
  const [patients, setPatients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  // Cargar pacientes al montar el componente
  useEffect(() => {
    fetchPatients();
  }, []);

  // Función para obtener pacientes de Supabase
  const fetchPatients = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('pacientes')
        .select(`
          *,
          psicologo:psicologo_id (
            id, nombre, apellido
          )
        `)
        .order('nombre', { ascending: true });

      if (error) throw error;
      setPatients(data || []);
    } catch (error) {
      console.error('Error al cargar pacientes:', error.message);
    } finally {
      setLoading(false);
    }
  };

  // Filtrar pacientes según el término de búsqueda
  const filteredPatients = patients.filter(patient => {
    const searchTermLower = searchTerm.toLowerCase();
    const nombre = (patient.nombre || '').toLowerCase();
    const apellido = (patient.apellido || patient.apellidos || '').toLowerCase();
    const genero = (patient.genero || '').toLowerCase();

    return (
      nombre.includes(searchTermLower) ||
      apellido.includes(searchTermLower) ||
      genero.includes(searchTermLower) ||
      `${nombre} ${apellido}`.includes(searchTermLower)
    );
  });

  return (
    <div className="container mx-auto py-6">
      <div className="mb-8">
        <div className="text-center">
          <div className="inline-flex items-center justify-center mb-3">
            <div className="bg-yellow-400 p-3 rounded-full mr-3 shadow-md">
              <FaUser className="text-white text-2xl" />
            </div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-800 to-indigo-950 bg-clip-text text-transparent">Pacientes</h1>
          </div>
          <p className="text-gray-600 max-w-2xl mx-auto">Lista de pacientes registrados en el sistema para evaluaciones psicométricas</p>
        </div>
      </div>

      {/* Barra de búsqueda */}
      <div className="mb-8">
        <div className="relative max-w-md mx-auto">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FaSearch className="text-blue-400" />
          </div>
          <input
            type="text"
            className="block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-full leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 shadow-sm"
            placeholder="Buscar paciente por nombre, apellido o género..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {/* Contenido principal */}
      <Card className="shadow-md border-0 rounded-xl overflow-hidden">
        <CardHeader className="bg-gradient-to-r from-blue-900 to-indigo-950 text-white border-0">
          <div className="flex items-center justify-center">
            <h2 className="text-xl font-semibold">Lista de Pacientes</h2>
            <span className="ml-3 bg-white text-blue-600 rounded-full px-3 py-1 text-sm font-medium">
              {filteredPatients.length}
            </span>
          </div>
        </CardHeader>
        <CardBody className="p-6">
          {loading ? (
            <div className="text-center py-12">
              <FaSpinner className="animate-spin text-blue-500 text-4xl mx-auto mb-4" />
              <p className="text-gray-600">Cargando pacientes...</p>
            </div>
          ) : filteredPatients.length === 0 ? (
            <div className="text-center py-12">
              {searchTerm ? (
                <div>
                  <p className="text-gray-500 mb-2">No se encontraron pacientes que coincidan con la búsqueda</p>
                  <button
                    className="text-blue-500 hover:text-blue-700"
                    onClick={() => setSearchTerm('')}
                  >
                    Limpiar búsqueda
                  </button>
                </div>
              ) : (
                <p className="text-gray-500">No hay pacientes registrados</p>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 p-2">
              {filteredPatients.map((patient) => (
                <PatientCard key={patient.id} patient={patient} />
              ))}
            </div>
          )}
        </CardBody>
      </Card>
    </div>
  );
};

export default Patients;
