import React from 'react';
import { Link } from 'react-router-dom';
import PropTypes from 'prop-types';
import './TestCard.css';

// Componente de tarjeta para los tests individuales
const TestCard = ({ test, iconClass, bgClass, textClass, buttonColor, abbreviation, showButton = true }) => {
  // Colores de los botones basados en los nombres de color
  const buttonColors = {
    blue: 'bg-blue-600 hover:bg-blue-700',
    green: 'bg-green-600 hover:bg-green-700',
    red: 'bg-red-600 hover:bg-red-700',
    amber: 'bg-amber-600 hover:bg-amber-700',
    indigo: 'bg-indigo-600 hover:bg-indigo-700',
    gray: 'bg-gray-700 hover:bg-gray-800',
    slate: 'bg-slate-600 hover:bg-slate-700',
    teal: 'bg-teal-600 hover:bg-teal-700',
    purple: 'bg-purple-600 hover:bg-purple-700',
    pink: 'bg-pink-600 hover:bg-pink-700'
  };

  // Color del círculo con la abreviatura
  const circleColors = {
    blue: 'bg-blue-600',
    green: 'bg-green-600',
    red: 'bg-red-600',
    amber: 'bg-amber-600',
    indigo: 'bg-indigo-600',
    gray: 'bg-gray-700',
    slate: 'bg-slate-600',
    teal: 'bg-teal-600',
    purple: 'bg-purple-600',
    pink: 'bg-pink-600'
  };

  return (
    <div className="test-card-container">
      <div className="test-card">
        {/* Círculo con abreviatura - siempre visible */}
        {abbreviation && (
          <div className={`abbreviation-circle ${circleColors[buttonColor]}`}>
            {abbreviation}
          </div>
        )}

        <div className="test-card-header">
          <div className={`test-card-icon ${bgClass}`}>
            <i className={`${iconClass} ${textClass}`}></i>
          </div>
          <h3 className="test-card-title">{test.title}</h3>
        </div>

        <div className="test-card-description">
          <p>{test.description}</p>
        </div>

        <div className="test-card-info-container">
          <div className="test-card-info">
            <span className="info-label">Tiempo</span>
            <span className="info-value">{test.time}</span>
            <span className="info-unit">minutos</span>
          </div>
          <div className="test-card-info">
            <span className="info-label">Preguntas</span>
            <span className="info-value">{test.questions}</span>
          </div>
        </div>

        {/* Botón de iniciar test - siempre visible */}
        <div className="test-card-button-container">
          <Link
            to={
              test.id === 'verbal'
                ? `/test/instructions/verbal`
                : test.id === 'espacial'
                  ? `/test/instructions/espacial`
                  : test.id === 'atencion'
                    ? `/test/instructions/atencion`
                    : `/test/instructions/${test.id}`
            }
            className={`test-card-button ${buttonColors[buttonColor]}`}
          >
            <i className="fas fa-play-circle mr-2"></i>
            Iniciar Test
          </Link>
        </div>
      </div>
    </div>
  );
};

TestCard.propTypes = {
  test: PropTypes.shape({
    id: PropTypes.string.isRequired,
    title: PropTypes.string.isRequired,
    description: PropTypes.string.isRequired,
    time: PropTypes.number.isRequired,
    questions: PropTypes.number.isRequired
  }).isRequired,
  iconClass: PropTypes.string.isRequired,
  bgClass: PropTypes.string.isRequired,
  textClass: PropTypes.string.isRequired,
  buttonColor: PropTypes.string.isRequired,
  abbreviation: PropTypes.string,
  showButton: PropTypes.bool
};

export default TestCard;