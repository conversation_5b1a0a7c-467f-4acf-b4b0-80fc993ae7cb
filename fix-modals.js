/**
 * Script para solucionar problemas con modales en el Panel de Administración
 * BAT-7 Evaluaciones - 02 de mayo de 2025
 * 
 * Este script corrige problemas con modales que no se muestran correctamente
 * o que quedan entrecortados en lugar de aparecer en primer plano.
 */

(function() {
  console.log('========================================');
  console.log('Iniciando corrección de modales BAT-7...');
  console.log('========================================');
  
  // 1. Asegurar que el elemento modal-root existe
  if (!document.getElementById('modal-root')) {
    const modalRoot = document.createElement('div');
    modalRoot.setAttribute('id', 'modal-root');
    // Asegurarnos que tiene el z-index correcto para sobresalir
    modalRoot.style.zIndex = '9999';
    // Posicionar al final del body para evitar problemas de posición
    document.body.appendChild(modalRoot);
    console.log('✅ Elemento modal-root creado correctamente');
  } else {
    // Si ya existe, asegurarnos que tiene el z-index correcto
    const existingRoot = document.getElementById('modal-root');
    existingRoot.style.zIndex = '9999';
    console.log('✅ Elemento modal-root ya existe, z-index actualizado');
  }
  
  // 2. Verificar implementación de ReactDOM
  if (typeof window.ReactDOM === 'undefined' && typeof window.React !== 'undefined') {
    console.log('❌ ReactDOM no está disponible. Creando implementación básica...');
    
    // Crear una implementación básica de ReactDOM si no está disponible
    window.ReactDOM = {
      ...window.React,
      createPortal: function(children, container) {
        console.log('Usando implementación básica de createPortal');
        // En lugar de usar un portal real, crear un elemento div y añadirlo al container
        if (container && typeof container.appendChild === 'function') {
          const portalContainer = document.createElement('div');
          // Asignar estilo para asegurar que sea visible en primer plano
          portalContainer.style.position = 'fixed';
          portalContainer.style.zIndex = '9999';
          portalContainer.style.top = '0';
          portalContainer.style.left = '0';
          portalContainer.style.width = '100%';
          portalContainer.style.height = '100%';
          container.appendChild(portalContainer);
          
          // Devolver los hijos directamente, como haría el componente Modal original
          return children;
        }
        return children;
      }
    };
    
    console.log('✅ Implementación básica de ReactDOM creada');
  } else {
    console.log('✅ ReactDOM está disponible');
  }
  
  // 3. Corregir estilos CSS para modales
  console.log('Aplicando correcciones de estilos CSS para modales...');
  
  // Crear un estilo que asegure que los modales siempre estén encima
  const styleElement = document.createElement('style');
  styleElement.textContent = `
    /* Estilos para asegurar que los modales se muestren correctamente */
    .fixed.inset-0.z-50, .fixed.inset-0.z-\\[9999\\] {
      position: fixed;
      z-index: 9999 !important;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
    }
    
    /* Asegurar que el backdrop del modal esté por encima de otros elementos */
    .fixed.inset-0.bg-black.bg-opacity-50 {
      z-index: 9998 !important;
    }
    
    /* Asegurar que el contenido del modal esté por encima del backdrop */
    .fixed.inset-0.z-50 > div, .fixed.inset-0.z-\\[9999\\] > div {
      z-index: 9999 !important;
    }
    
    /* Forzar que el body tenga posición relativa para que los modales funcionen correctamente */
    body {
      position: relative;
    }
    
    /* Estilos para el modal-root */
    #modal-root {
      position: relative;
      z-index: 9999;
    }
  `;
  
  // Añadir el estilo al documento
  document.head.appendChild(styleElement);
  console.log('✅ Estilos CSS para modales aplicados');
  
  // 4. Corregir problemas con botones bloqueados
  console.log('Verificando botones bloqueados...');
  
  // Buscar todos los botones que podrían estar deshabilitados incorrectamente
  const disabledButtons = document.querySelectorAll('button[disabled]');
  let unblocked = 0;
  
  disabledButtons.forEach(button => {
    // Verificar si el botón debería estar habilitado (botones para añadir elementos nuevos)
    if (
      button.innerText.includes('Nuevo') || 
      button.innerText.includes('Nueva') || 
      button.innerText.includes('Agregar') ||
      button.innerText.includes('Editar')
    ) {
      // Habilitar el botón
      button.disabled = false;
      button.removeAttribute('disabled');
      unblocked++;
    }
  });
  
  if (unblocked > 0) {
    console.log(`✅ Se habilitaron ${unblocked} botones que estaban bloqueados`);
  } else {
    console.log('✅ No se encontraron botones bloqueados incorrectamente');
  }
  
  // 5. Forzar actualización de la interfaz
  console.log('Forzando actualización de la interfaz...');
  
  // Disparar un evento de resize para forzar actualización de componentes
  window.dispatchEvent(new Event('resize'));
  
  // También simular un cambio de pestaña para refrescar completamente
  const tabs = document.querySelectorAll('[role="tab"]');
  if (tabs.length > 0) {
    // Encontrar la pestaña activa actual
    let activeTabIndex = 0;
    tabs.forEach((tab, index) => {
      if (tab.getAttribute('aria-selected') === 'true') {
        activeTabIndex = index;
      }
    });
    
    // Simular clic en otra pestaña y luego volver a la original
    const nextTab = tabs[(activeTabIndex + 1) % tabs.length];
    const originalTab = tabs[activeTabIndex];
    
    if (nextTab && originalTab) {
      // Simular clic en la siguiente pestaña
      nextTab.click();
      
      // Esperar un momento y volver a la pestaña original
      setTimeout(() => {
        originalTab.click();
        console.log('✅ Interfaz actualizada correctamente');
        
        // Mensaje final
        console.log('========================================');
        console.log('✅ Corrección de modales completada con éxito');
        console.log('Si sigues teniendo problemas, intenta recargar la página');
        console.log('========================================');
      }, 300);
    } else {
      console.log('⚠️ No se pudo cambiar de pestaña automáticamente');
      console.log('========================================');
      console.log('✅ Corrección de modales aplicada parcialmente');
      console.log('Por favor, cambia manualmente entre pestañas para refrescar la interfaz');
      console.log('========================================');
    }
  } else {
    console.log('⚠️ No se encontraron pestañas para simular cambio');
    console.log('========================================');
    console.log('✅ Corrección de modales aplicada parcialmente');
    console.log('Por favor, recarga la página para aplicar todos los cambios');
    console.log('========================================');
  }
})();
