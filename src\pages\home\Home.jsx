import React from 'react';
import { Card, CardBody, CardHeader } from '../../components/ui/Card';

const Home = () => {

  return (
    <div>
      <div className="mb-8">
        <Card className="shadow-md rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-30">
          <CardBody>
            <div className="w-full h-auto rounded-lg mb-6 flex items-center justify-center overflow-hidden">
              <img
                src="/assets/images/banner.png"
                alt="BAT-7 Evaluación de Aptitudes"
                className="w-full object-cover h-[350px]"
              />
            </div>
          </CardBody>
        </Card>
      </div>

      <div className="mb-8">
        <Card className="shadow-md rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-300">
          <CardHeader className="bg-gradient-to-r from-blue-50 to-blue-100 border-b border-blue-200">
            <h3 className="text-xl font-semibold text-blue-700 text-center py-2 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Descripción
            </h3>
          </CardHeader>
          <CardBody>
            <p className="text-gray-700 mb-4 text-center leading-relaxed px-4 py-2">
              A través de esta plataforma digital, podrás realizar la prueba de forma segura y eficiente. El objetivo es obtener
              una visión integral de tus fortalezas y potencial, asegurando un proceso de selección equitativo y orientado a
              identificar a los candidatos mejor preparados para los desafíos y oportunidades que ofrece cada institución.
            </p>
          </CardBody>
        </Card>
      </div>

      <div className="mb-8">
        <Card className="shadow-md rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-300">
          <CardHeader className="bg-gradient-to-r from-green-50 to-green-100 border-b border-green-200">
            <h3 className="text-xl font-semibold text-green-700 text-center py-2 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              Aptitudes evaluadas
            </h3>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              {/* Primera fila */}
              <div className="flex flex-col items-center text-center">
                <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mb-2">
                  <span className="font-semibold">V</span>
                </div>
                <span className="font-medium">Verbal</span>
                <p className="text-sm text-gray-600 mt-1">Comprensión y razonamiento verbal</p>
              </div>

              <div className="flex flex-col items-center text-center">
                <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center text-green-600 mb-2">
                  <span className="font-semibold">E</span>
                </div>
                <span className="font-medium">Espacial</span>
                <p className="text-sm text-gray-600 mt-1">Visualización y orientación espacial</p>
              </div>

              <div className="flex flex-col items-center text-center">
                <div className="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center text-purple-600 mb-2">
                  <span className="font-semibold">A</span>
                </div>
                <span className="font-medium">Atención</span>
                <p className="text-sm text-gray-600 mt-1">Capacidad de atención sostenida</p>
              </div>

              <div className="flex flex-col items-center text-center">
                <div className="w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center text-yellow-600 mb-2">
                  <span className="font-semibold">CON</span>
                </div>
                <span className="font-medium">Concentración</span>
                <p className="text-sm text-gray-600 mt-1">Enfoque y concentración mental</p>
              </div>

              {/* Segunda fila */}
              <div className="flex flex-col items-center text-center">
                <div className="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center text-red-600 mb-2">
                  <span className="font-semibold">R</span>
                </div>
                <span className="font-medium">Razonamiento</span>
                <p className="text-sm text-gray-600 mt-1">Pensamiento lógico y analítico</p>
              </div>

              <div className="flex flex-col items-center text-center">
                <div className="w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600 mb-2">
                  <span className="font-semibold">N</span>
                </div>
                <span className="font-medium">Numérica</span>
                <p className="text-sm text-gray-600 mt-1">Habilidades matemáticas y cálculo</p>
              </div>

              <div className="flex flex-col items-center text-center">
                <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 mb-2">
                  <span className="font-semibold">M</span>
                </div>
                <span className="font-medium">Mecánica</span>
                <p className="text-sm text-gray-600 mt-1">Comprensión de principios físicos</p>
              </div>

              <div className="flex flex-col items-center text-center">
                <div className="w-10 h-10 rounded-full bg-pink-100 flex items-center justify-center text-pink-600 mb-2">
                  <span className="font-semibold">O</span>
                </div>
                <span className="font-medium">Ortografía</span>
                <p className="text-sm text-gray-600 mt-1">Corrección en la escritura</p>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      <div className="mb-8">
        <Card className="shadow-md rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-300">
          <CardHeader className="bg-gradient-to-r from-purple-50 to-purple-100 border-b border-purple-200">
            <h3 className="text-xl font-semibold text-purple-700 text-center py-2 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
              </svg>
              Características
            </h3>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 rounded-lg bg-blue-100 flex items-center justify-center text-blue-600 mb-4 mx-auto">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-9 w-9" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
                <h3 className="text-xl font-medium text-gray-800 mb-3">Seguridad y Privacidad</h3>
                <p className="text-gray-600">
                  Tu información y resultados están protegidos. Utilizamos protocolos de seguridad avanzados para garantizar la confidencialidad de tus datos.
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 rounded-lg bg-green-100 flex items-center justify-center text-green-600 mb-4 mx-auto">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-9 w-9" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                  </svg>
                </div>
                <h3 className="text-xl font-medium text-gray-800 mb-3">Resultados Detallados</h3>
                <p className="text-gray-600">
                  Al finalizar, recibirás un informe completo que detalla tus aptitudes, fortalezas y áreas de mejora en cada componente evaluado.
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 rounded-lg bg-purple-100 flex items-center justify-center text-purple-600 mb-4 mx-auto">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-9 w-9" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-medium text-gray-800 mb-3">Proceso Eficiente</h3>
                <p className="text-gray-600">
                  La evaluación está diseñada para ser completada en un tiempo razonable, con instrucciones claras y una interfaz intuitiva.
                </p>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};

export default Home;