import React, { useState, useRef, useEffect } from 'react';
import { Outlet, Link, useLocation } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import PageTransition from '../transitions/PageTransition';

// Componente Sidebar con favoritos
const Sidebar = ({ isOpen, toggleSidebar }) => {
  const location = useLocation();

  // Inicializar favoritos desde localStorage o usar valores predeterminados
  const [favorites, setFavorites] = useState(() => {
    const savedFavorites = localStorage.getItem('sidebarFavorites');
    return savedFavorites ? JSON.parse(savedFavorites) : {
      dashboard: false,
      home: false,
      patients: false,
      tests: false,
      reports: false,
      administration: false,
      settings: false,
      help: false
    };
  });

  // Guardar favoritos en localStorage cuando cambien
  useEffect(() => {
    localStorage.setItem('sidebarFavorites', JSON.stringify(favorites));
  }, [favorites]);

  // Toggle para favoritos
  const toggleFavorite = (key, e) => {
    e.preventDefault();
    e.stopPropagation();
    setFavorites(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  // Verificar si una ruta está activa
  const isActive = (path) => {
    if (path === '/') {
      return location.pathname === '/';
    }

    return location.pathname === path ||
           (location.pathname.startsWith(path) &&
            (location.pathname.length === path.length ||
             location.pathname[path.length] === '/'));
  };

  // Elementos del menú
  const menuItems = [
    { name: 'Dashboard', path: '/admin/administration', icon: 'th', key: 'dashboard' },
    { name: 'Inicio', path: '/home', icon: 'home', key: 'home' },
    { name: 'Pacientes', path: '/student/patients', icon: 'user', key: 'patients' },
    { name: 'Cuestionario', path: '/student/tests', icon: 'clipboard', key: 'tests' },
    { name: 'Resultados', path: '/admin/reports', icon: 'chart-bar', key: 'reports' },
    { name: 'Administración', path: '/admin/administration', icon: 'shield-alt', key: 'administration' },
    { name: 'Configuración', path: '/settings', icon: 'cog', key: 'settings' },
    { name: 'Ayuda', path: '/help', icon: 'question-circle', key: 'help' }
  ];

  // Filtrar favoritos
  const favoriteItems = menuItems.filter(item => favorites[item.key]);

  return (
    <div className={`sidebar bg-[#121940] text-[#a4b1cd] fixed top-0 left-0 h-full z-50 transition-all duration-300 ease-in-out
                     ${isOpen ? 'w-64' : 'w-[70px]'}`}>
      <div className="sidebar-header p-5 flex justify-between items-center border-b border-opacity-10 border-white">
        {isOpen && (
          <h1 className="text-xl font-bold text-white">
            Activatu<span className="text-[#ffda0a]">mente</span>
          </h1>
        )}
        <button
          onClick={toggleSidebar}
          className="text-[#a4b1cd] cursor-pointer"
        >
          <i className={`fas ${isOpen ? 'fa-chevron-left' : 'fa-chevron-right'}`}></i>
        </button>
      </div>

      {/* Sección de favoritos */}
      {favoriteItems.length > 0 && (
        <div className="sidebar-section py-2 border-b border-opacity-10 border-white">
          {isOpen && (
            <h2 className="uppercase text-xs px-5 py-2 tracking-wider font-semibold text-gray-400">
              FAVORITOS
            </h2>
          )}
          <ul className="menu-list">
            {favoriteItems.map((item) => (
              <li
                key={`fav-${item.key}`}
                className={`py-2 px-5 hover:bg-opacity-5 hover:bg-white transition-all duration-200 relative
                          ${isActive(item.path) ? 'bg-[#ffda0a] bg-opacity-10 text-white border-l-4 border-[#ffda0a]' : ''}`}
              >
                <div className="flex items-center justify-between w-full">
                  <Link
                    to={item.path}
                    className="flex items-center flex-grow"
                  >
                    <i className={`fas fa-${item.icon} ${!isOpen ? '' : 'mr-3'} w-5 text-center ${isActive(item.path) ? 'text-[#ffda0a]' : ''}`}></i>
                    {isOpen && <span>{item.name}</span>}
                  </Link>
                  {isOpen && (
                    <span
                      className="text-[#ffda0a] cursor-pointer"
                      onClick={(e) => toggleFavorite(item.key, e)}
                      title="Quitar de favoritos"
                    >
                      <i className="fas fa-star"></i>
                    </span>
                  )}
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Menú principal */}
      <div className="sidebar-content py-2">
        <ul className="menu-list">
          {menuItems.map((item) => (
            <li
              key={item.name}
              className={`py-2 px-5 hover:bg-opacity-5 hover:bg-white transition-all duration-200 relative
                        ${isActive(item.path) ? 'bg-[#ffda0a] bg-opacity-10 text-white border-l-4 border-[#ffda0a]' : ''}`}
            >
              <div className="flex items-center justify-between w-full">
                <Link
                  to={item.path}
                  className="flex items-center flex-grow"
                >
                  <i className={`fas fa-${item.icon} ${!isOpen ? '' : 'mr-3'} w-5 text-center ${isActive(item.path) ? 'text-[#ffda0a]' : ''}`}></i>
                  {isOpen && <span>{item.name}</span>}
                </Link>
                {isOpen && (
                  <span
                    className={`cursor-pointer hover:text-[#ffda0a] transition-colors duration-200 ${favorites[item.key] ? 'text-[#ffda0a]' : 'text-gray-400'}`}
                    onClick={(e) => toggleFavorite(item.key, e)}
                    title={favorites[item.key] ? "Quitar de favoritos" : "Añadir a favoritos"}
                  >
                    <i className={`${favorites[item.key] ? 'fas' : 'far'} fa-star`}></i>
                  </span>
                )}
              </div>
            </li>
          ))}
        </ul>
      </div>

      {/* Cerrar Sesión */}
      <div className="mt-auto p-5 border-t border-opacity-10 border-white">
        {isOpen && (
          <div className="flex items-center text-gray-400 hover:text-white cursor-pointer">
            <i className="fas fa-sign-out-alt mr-3"></i>
            <span>Cerrar Sesión</span>
          </div>
        )}
      </div>
    </div>
  );
};

const Layout = () => {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const userMenuRef = useRef(null);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const toggleUserMenu = () => {
    setUserMenuOpen(!userMenuOpen);
  };

  // Cerrar el menú de usuario cuando se hace clic fuera de él
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {
        setUserMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar con favoritos */}
      <Sidebar isOpen={sidebarOpen} toggleSidebar={toggleSidebar} />

      {/* Contenido principal */}
      <div className={`flex-1 transition-all duration-300 ease-in-out
                    ${sidebarOpen ? 'ml-64' : 'ml-[70px]'}`}>
        {/* Header */}
        <header className="bg-white shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16 items-center">
              <div className="flex items-center">
                <h1 className="text-xl font-bold text-blue-600 text-center">BAT-7</h1>
              </div>

              {/* Información del usuario */}
              <div className="flex items-center relative" ref={userMenuRef}>
                <div
                  className="flex items-center space-x-3 cursor-pointer"
                  onClick={toggleUserMenu}
                >
                  <div className="flex flex-col items-end">
                    <span className="text-sm font-medium text-gray-800">Administrador</span>
                  </div>
                  <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
                    <i className="fas fa-user"></i>
                  </div>
                </div>

                {/* Menú desplegable del usuario */}
                {userMenuOpen && (
                  <div className="absolute right-0 top-full mt-2 w-48 bg-white rounded-md shadow-lg z-10 py-1">
                    <div className="px-4 py-2 border-b border-gray-100">
                      <p className="text-sm font-medium text-gray-900">Administrador</p>
                    </div>

                    <Link
                      to="/settings"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <i className="fas fa-cog mr-2"></i> Configuración
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </div>
        </header>

        {/* Contenido principal */}
        <main className="py-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Contenido de la página con transición */}
            <PageTransition>
              <Outlet />
            </PageTransition>
          </div>
        </main>

        {/* Footer */}
        <footer className="bg-white border-t border-gray-200 py-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <p className="text-center text-gray-500 text-sm">
              &copy; {new Date().getFullYear()} BAT-7 Evaluaciones. Todos los derechos reservados.
            </p>
          </div>
        </footer>

        {/* Contenedor de notificaciones Toast */}
        <ToastContainer
          position="top-right"
          autoClose={5000}
          hideProgressBar={false}
          newestOnTop
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
        />
      </div>
    </div>
  );
};

export default Layout;