import React, { createContext, useContext, useState, useEffect } from 'react';
import { toast } from 'react-toastify';

// Create authentication context
const AuthContext = createContext();

// Usuario mock para autenticación desactivada
const mockUser = {
  id: '00000000-0000-0000-0000-000000000000',
  email: '<EMAIL>',
  tipo_usuario: 'Administrador',
  rol: 'Administrador',
  nombre: 'Usuario',
  apellido: 'Administrador',
  documento: '12345678',
  fecha_creacion: new Date().toISOString(),
  ultimo_acceso: new Date().toISOString(),
  activo: true
};

// Authentication context provider
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(mockUser);
  const [loading, setLoading] = useState(false);

  // Efecto simplificado para autenticación desactivada
  useEffect(() => {
    console.log('[AuthContext] Autenticación desactivada - usando usuario mock');

    // Guardar usuario mock en localStorage para mantener consistencia
    localStorage.setItem('user', JSON.stringify(mockUser));
    localStorage.setItem('token', 'mock-token-for-disabled-auth');

    // No hay necesidad de verificar sesión, siempre estamos autenticados
    setLoading(false);
  }, []);

  // Funciones de autenticación simplificadas para autenticación desactivada

  // Login function - siempre devuelve éxito con el usuario mock
  const login = async ({ email, password }, remember = false) => {
    console.log('[AuthContext] Login simulado con autenticación desactivada');

    // Guardar usuario mock en localStorage para mantener consistencia
    localStorage.setItem('user', JSON.stringify(mockUser));
    localStorage.setItem('token', 'mock-token-for-disabled-auth');

    // Siempre devolver éxito con el usuario mock
    return {
      success: true,
      user: mockUser
    };
  };

  // Register function - siempre devuelve éxito
  const register = async ({ email, password, ...userData }) => {
    console.log('[AuthContext] Registro simulado con autenticación desactivada');

    // Mostrar mensaje de éxito
    toast.success('Registro simulado exitoso (autenticación desactivada)');

    return {
      success: true,
      user: mockUser,
      message: 'Registro simulado exitoso (autenticación desactivada)'
    };
  };

  // Logout function - no hace nada realmente
  const logout = async () => {
    console.log('[AuthContext] Logout simulado con autenticación desactivada');

    // Mostrar mensaje
    toast.info('Cierre de sesión simulado (autenticación desactivada)');

    return { success: true };
  };

  /**
   * Function to send a password reset link - simulada
   * @param {string} email - User's email
   * @returns {Promise<Object>} Operation result
   */
  const resetPassword = async (email) => {
    console.log('[AuthContext] Restablecimiento de contraseña simulado');

    // Mostrar mensaje
    toast.info('Enlace de restablecimiento simulado (autenticación desactivada)');

    return {
      success: true,
      message: 'Enlace de restablecimiento simulado (autenticación desactivada)'
    };
  };

  /**
   * Function to update user's password - simulada
   * @param {string} newPassword - New password
   * @returns {Promise<Object>} Operation result
   */
  const updatePassword = async (newPassword) => {
    console.log('[AuthContext] Actualización de contraseña simulada');

    // Mostrar mensaje
    toast.info('Contraseña actualizada simulada (autenticación desactivada)');

    return {
      success: true,
      message: 'Contraseña actualizada simulada (autenticación desactivada)'
    };
  };

  // Context value con autenticación desactivada - siempre autenticado como administrador
  const value = {
    // Authentication state - siempre autenticado
    user,
    loading: false,
    isAuthenticated: true,

    // Authentication functions simuladas
    login,
    register,
    logout,
    resetPassword,
    updatePassword,

    // Roles y permisos - siempre administrador
    isAdmin: true,
    isPsychologist: false,
    isStudent: false,

    // Propiedades de usuario - valores del usuario mock
    userRole: 'Administrador',
    userId: mockUser.id,
    userEmail: mockUser.email,
    userDocumento: mockUser.documento,
    userName: `${mockUser.nombre} ${mockUser.apellido}`,
    userFirstName: mockUser.nombre,
    userLastName: mockUser.apellido,

    // Información de sesión
    sessionCreated: mockUser.fecha_creacion,
    lastAccess: mockUser.ultimo_acceso
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Custom hook to use the authentication context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;