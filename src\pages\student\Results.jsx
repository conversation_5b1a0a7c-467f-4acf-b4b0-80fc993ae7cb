import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Card, CardHeader, CardBody } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { useToast } from '../../hooks/useToast';

const Results = () => {
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(true);
  const { showToast } = useToast();

  useEffect(() => {
    // Simular carga de datos
    const fetchData = async () => {
      try {
        // En producción, aquí se haría una llamada a la API
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // Datos simulados
        const mockResults = [
          { 
            id: '12345',
            testName: 'Test de Aptitud Verbal',
            completedDate: new Date(2025, 3, 15),
            score: 82,
            percentile: 72,
            interpretation: 'Alto',
          },
          { 
            id: '12346',
            testName: 'Test de Razonamiento Lógico',
            completedDate: new Date(2025, 3, 10),
            score: 75,
            percentile: 64,
            interpretation: 'Medio-Alto',
          },
          { 
            id: '12347',
            testName: 'Test de Aptitud Numérica',
            completedDate: new Date(2025, 2, 25),
            score: 68,
            percentile: 54,
            interpretation: 'Medio',
          },
        ];
        
        setResults(mockResults);
        setLoading(false);
      } catch (error) {
        console.error('Error al cargar resultados:', error);
        showToast('Error al cargar los resultados', 'error');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString();
  };

  const getScoreColor = (score) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBgColor = (score) => {
    if (score >= 80) return 'bg-green-100';
    if (score >= 60) return 'bg-yellow-100';
    return 'bg-red-100';
  };

  const getInterpretationColor = (interpretation) => {
    switch (interpretation) {
      case 'Muy Alto':
      case 'Alto':
        return 'text-green-600 bg-green-50';
      case 'Medio-Alto':
      case 'Medio':
        return 'text-blue-600 bg-blue-50';
      case 'Medio-Bajo':
      case 'Bajo':
      case 'Muy Bajo':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Mis Resultados</h1>
      </div>

      {loading ? (
        <div className="py-16 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-500">Cargando resultados...</p>
        </div>
      ) : (
        <>
          {results.length === 0 ? (
            <Card>
              <CardBody>
                <div className="py-8 text-center">
                  <p className="text-gray-500">No has completado ningún test todavía.</p>
                </div>
              </CardBody>
            </Card>
          ) : (
            <div className="grid grid-cols-1 gap-6">
              {results.map((result) => (
                <Card key={result.id} className="overflow-hidden">
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200">
                    <div className="flex justify-between items-center">
                      <div>
                        <h2 className="text-lg font-medium text-gray-800">{result.testName}</h2>
                        <p className="text-sm text-gray-500">
                          Completado: {formatDate(result.completedDate)}
                        </p>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className={`px-3 py-1 rounded-full text-sm font-medium ${getInterpretationColor(result.interpretation)}`}>
                          {result.interpretation}
                        </div>
                        <div className={`text-2xl font-bold ${getScoreColor(result.score)}`}>
                          {result.score}
                        </div>
                      </div>
                    </div>
                  </div>
                  <CardBody>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center space-x-6">
                        <div>
                          <p className="text-sm text-gray-500">Percentil</p>
                          <p className="text-lg font-medium">{result.percentile}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Interpretación</p>
                          <p className="text-lg font-medium">{result.interpretation}</p>
                        </div>
                      </div>
                      <div>
                        <Button
                          as={Link}
                          to={`/test/results/${result.id}`}
                          variant="primary"
                        >
                          Ver Detalles
                        </Button>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default Results;