/* Estilos para el contenedor externo */
.test-card-container {
  height: 100%;
  display: flex;
}

/* Estilos para las tarjetas de test */
.test-card {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.05);
  padding: 20px;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  height: 340px; /* Altura fija para todas las tarjetas */
}

.test-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
}

/* Círculo con abreviatura */
.abbreviation-circle {
  position: absolute;
  top: 15px;
  right: 15px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 13px;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

/* Cabecera de la tarjeta */
.test-card-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 15px;
  height: 100px; /* Altura fija para la cabecera */
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* Icono de la tarjeta */
.test-card-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
}

.test-card-icon i {
  font-size: 24px !important;
  line-height: 1;
  vertical-align: middle;
  font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro", "Font Awesome 6 Brands";
  font-weight: 900;
}

/* Título de la tarjeta */
.test-card-title {
  font-size: 18px;
  font-weight: 500;
  color: #1f2937;
  text-align: center;
  margin: 0;
  padding: 0 15px;
  line-height: 1.3;
}

/* Descripción */
.test-card-description {
  height: 70px; /* Altura fija para la descripción */
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 5px;
  margin-bottom: 20px;
  overflow: hidden;
}

.test-card-description p {
  font-size: 14px;
  color: #6b7280;
  text-align: center;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  max-height: 100%;
}

/* Contenedor de información de tiempo y preguntas */
.test-card-info-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 20px;
  height: 80px; /* Altura fija para el contenedor de info */
}

/* Información de tiempo y preguntas */
.test-card-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  height: 100%;
}

.info-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.info-value {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  line-height: 1;
}

.info-unit {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

/* Contenedor para el botón */
.test-card-button-container {
  margin-top: auto;
  height: 48px; /* Altura fija para el contenedor del botón */
}

/* Botón de iniciar test */
.test-card-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  transition: all 0.2s ease;
  text-decoration: none;
  height: 100%;
  width: 100%;
}